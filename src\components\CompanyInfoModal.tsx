import React, { useState, useEffect, useRef } from 'react';
import { useData } from '../contexts/DataContext';
import { useTheme } from '../contexts/ThemeContext';
import { CompanyInfo } from '../types';

interface CompanyInfoModalProps {
  isVisible: boolean;
  onClose: () => void;
}

const CompanyInfoModal: React.FC<CompanyInfoModalProps> = ({ isVisible, onClose }) => {
  const { state, saveCompanyInfo } = useData();
  const { isDark } = useTheme();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState({
    name: '',
    street: '',
    city: '',
    state: '',
    postalCode: '',
    country: '',
    phone: '',
    email: '',
    taxId: '',
    website: '',
  });

  const [logo, setLogo] = useState<string>('');
  const [logoPreview, setLogoPreview] = useState<string>('');
  const [isLoading, setSaving] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);

  // Load existing company info when modal opens
  useEffect(() => {
    if (isVisible && state.companyInfo) {
      setFormData({
        name: state.companyInfo.name,
        street: state.companyInfo.address.street,
        city: state.companyInfo.address.city,
        state: state.companyInfo.address.state,
        postalCode: state.companyInfo.address.postalCode,
        country: state.companyInfo.address.country,
        phone: state.companyInfo.phone,
        email: state.companyInfo.email,
        taxId: state.companyInfo.taxId,
        website: state.companyInfo.website || '',
      });
      setLogo(state.companyInfo.logo || '');
      setLogoPreview(state.companyInfo.logo || '');
      setHasUnsavedChanges(false);
    } else if (isVisible) {
      // Reset form for new company info
      setFormData({
        name: '',
        street: '',
        city: '',
        state: '',
        postalCode: '',
        country: '',
        phone: '',
        email: '',
        taxId: '',
        website: '',
      });
      setLogo('');
      setLogoPreview('');
      setHasUnsavedChanges(false);
    }
    setErrors({});
  }, [isVisible, state.companyInfo]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    const validTypes = ['image/png', 'image/jpg', 'image/jpeg', 'image/svg+xml'];
    if (!validTypes.includes(file.type)) {
      setErrors(prev => ({ ...prev, logo: 'Please select a PNG, JPG, JPEG, or SVG file.' }));
      return;
    }

    // Validate file size (2MB max)
    if (file.size > 2 * 1024 * 1024) {
      setErrors(prev => ({ ...prev, logo: 'File size must be less than 2MB.' }));
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      setLogo(result);
      setLogoPreview(result);
      setHasUnsavedChanges(true);
      setErrors(prev => ({ ...prev, logo: '' }));
    };
    reader.readAsDataURL(file);
  };

  const removeLogo = () => {
    setLogo('');
    setLogoPreview('');
    setHasUnsavedChanges(true);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Company name is required.';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email address is required.';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address.';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required.';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) return;

    setSaving(true);
    try {
      const companyInfo: Omit<CompanyInfo, 'createdAt' | 'updatedAt'> = {
        id: state.companyInfo?.id || 'default',
        name: formData.name.trim(),
        address: {
          street: formData.street.trim(),
          city: formData.city.trim(),
          state: formData.state.trim(),
          postalCode: formData.postalCode.trim(),
          country: formData.country.trim(),
        },
        phone: formData.phone.trim(),
        email: formData.email.trim(),
        taxId: formData.taxId.trim(),
        website: formData.website.trim() || undefined,
        logo: logo || undefined,
      };

      await saveCompanyInfo(companyInfo);
      setHasUnsavedChanges(false);
      setShowSuccessMessage(true);

      // Show success message briefly then close
      setTimeout(() => {
        setShowSuccessMessage(false);
        onClose();
      }, 1500);
    } catch (error) {
      console.error('Error saving company info:', error);
      setErrors({ general: 'Failed to save company information. Please try again.' });
    } finally {
      setSaving(false);
    }
  };

  const handleClose = () => {
    if (hasUnsavedChanges) {
      if (window.confirm('You have unsaved changes. Are you sure you want to close?')) {
        onClose();
      }
    } else {
      onClose();
    }
  };

  if (!isVisible) return null;

  const modalStyle: React.CSSProperties = {
    position: 'fixed',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10000,
    padding: '10px',
  };

  const contentStyle: React.CSSProperties = {
    backgroundColor: isDark ? '#374151' : '#ffffff',
    borderRadius: '16px',
    padding: window.innerWidth < 640 ? '20px' : '32px',
    maxWidth: '600px',
    width: '100%',
    maxHeight: '95vh',
    overflowY: 'auto',
    boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    border: `1px solid ${isDark ? '#4b5563' : '#e5e7eb'}`,
    position: 'relative',
  };

  const headerStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '24px',
    paddingBottom: '16px',
    borderBottom: `1px solid ${isDark ? '#4b5563' : '#e5e7eb'}`,
  };

  const titleStyle: React.CSSProperties = {
    fontSize: '24px',
    fontWeight: 'bold',
    color: isDark ? '#f9fafb' : '#1f2937',
    margin: 0,
  };

  const closeButtonStyle: React.CSSProperties = {
    background: 'none',
    border: 'none',
    fontSize: '24px',
    cursor: 'pointer',
    color: isDark ? '#9ca3af' : '#6b7280',
    padding: '4px',
    borderRadius: '4px',
  };

  const inputGroupStyle: React.CSSProperties = {
    marginBottom: '20px',
  };

  const labelStyle: React.CSSProperties = {
    display: 'block',
    fontSize: '14px',
    fontWeight: '600',
    color: isDark ? '#f3f4f6' : '#374151',
    marginBottom: '6px',
  };

  const inputStyle: React.CSSProperties = {
    width: '100%',
    padding: '12px',
    border: `1px solid ${isDark ? '#4b5563' : '#d1d5db'}`,
    borderRadius: '8px',
    fontSize: '16px',
    backgroundColor: isDark ? '#1f2937' : '#ffffff',
    color: isDark ? '#f9fafb' : '#1f2937',
    boxSizing: 'border-box',
  };

  const errorStyle: React.CSSProperties = {
    color: '#ef4444',
    fontSize: '14px',
    marginTop: '4px',
  };

  const logoSectionStyle: React.CSSProperties = {
    marginBottom: '20px',
    padding: '16px',
    border: `2px dashed ${isDark ? '#4b5563' : '#d1d5db'}`,
    borderRadius: '8px',
    textAlign: 'center',
  };

  const buttonGroupStyle: React.CSSProperties = {
    display: 'flex',
    gap: '12px',
    justifyContent: 'flex-end',
    marginTop: '32px',
    paddingTop: '16px',
    borderTop: `1px solid ${isDark ? '#4b5563' : '#e5e7eb'}`,
  };

  const buttonStyle: React.CSSProperties = {
    padding: '12px 24px',
    borderRadius: '8px',
    fontSize: '16px',
    fontWeight: '600',
    cursor: 'pointer',
    border: 'none',
  };

  const primaryButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: isLoading || !formData.name || !formData.email || !formData.phone ? '#9ca3af' : '#667eea',
    color: '#ffffff',
  };

  const secondaryButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: 'transparent',
    color: isDark ? '#f3f4f6' : '#374151',
    border: `2px solid ${isDark ? '#4b5563' : '#d1d5db'}`,
  };

  return (
    <div style={modalStyle} onClick={(e) => e.target === e.currentTarget && handleClose()}>
      <div style={contentStyle}>
        <div style={headerStyle}>
          <h2 style={titleStyle}>Edit Company Information</h2>
          <button style={closeButtonStyle} onClick={handleClose}>
            ×
          </button>
        </div>

        {errors.general && (
          <div style={{ ...errorStyle, marginBottom: '16px', textAlign: 'center' }}>
            {errors.general}
          </div>
        )}

        {showSuccessMessage && (
          <div style={{
            backgroundColor: '#10b981',
            color: '#ffffff',
            padding: '12px',
            borderRadius: '8px',
            marginBottom: '16px',
            textAlign: 'center',
            fontSize: '14px',
            fontWeight: '600'
          }}>
            ✓ Company information saved successfully!
          </div>
        )}

        {/* Logo Section */}
        <div style={logoSectionStyle}>
          <label style={labelStyle}>Company Logo</label>
          {logoPreview ? (
            <div style={{ marginBottom: '12px' }}>
              <img 
                src={logoPreview} 
                alt="Logo preview" 
                style={{ 
                  maxWidth: '200px', 
                  maxHeight: '100px', 
                  objectFit: 'contain',
                  borderRadius: '4px',
                  border: `1px solid ${isDark ? '#4b5563' : '#d1d5db'}`,
                }} 
              />
              <div style={{ marginTop: '8px' }}>
                <button
                  type="button"
                  onClick={removeLogo}
                  style={{
                    ...buttonStyle,
                    backgroundColor: '#ef4444',
                    color: '#ffffff',
                    fontSize: '14px',
                    padding: '8px 16px',
                  }}
                >
                  Remove Logo
                </button>
              </div>
            </div>
          ) : (
            <div style={{ 
              padding: '32px', 
              color: isDark ? '#9ca3af' : '#6b7280',
              fontSize: '14px',
            }}>
              No logo uploaded
            </div>
          )}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/png,image/jpg,image/jpeg,image/svg+xml"
            onChange={handleLogoUpload}
            style={{ display: 'none' }}
          />
          <button
            type="button"
            onClick={() => fileInputRef.current?.click()}
            style={{
              ...buttonStyle,
              backgroundColor: isDark ? '#4b5563' : '#f3f4f6',
              color: isDark ? '#f3f4f6' : '#374151',
              fontSize: '14px',
              padding: '8px 16px',
            }}
          >
            {logoPreview ? 'Change Logo' : 'Upload Logo'}
          </button>
          {errors.logo && <div style={errorStyle}>{errors.logo}</div>}
        </div>

        {/* Company Name */}
        <div style={inputGroupStyle}>
          <label style={labelStyle}>
            Company Name <span style={{ color: '#ef4444' }}>*</span>
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            style={inputStyle}
            placeholder="Your Company Name"
          />
          {errors.name && <div style={errorStyle}>{errors.name}</div>}
        </div>

        {/* Address Fields */}
        <div style={inputGroupStyle}>
          <label style={labelStyle}>Street Address</label>
          <input
            type="text"
            value={formData.street}
            onChange={(e) => handleInputChange('street', e.target.value)}
            style={inputStyle}
            placeholder="123 Business Street"
          />
        </div>

        <div style={{ display: window.innerWidth < 640 ? 'block' : 'flex', gap: '12px', marginBottom: '20px' }}>
          <div style={{ flex: 1, marginBottom: window.innerWidth < 640 ? '20px' : '0' }}>
            <label style={labelStyle}>City</label>
            <input
              type="text"
              value={formData.city}
              onChange={(e) => handleInputChange('city', e.target.value)}
              style={inputStyle}
              placeholder="City"
            />
          </div>
          <div style={{ flex: 1 }}>
            <label style={labelStyle}>State/Province</label>
            <input
              type="text"
              value={formData.state}
              onChange={(e) => handleInputChange('state', e.target.value)}
              style={inputStyle}
              placeholder="State"
            />
          </div>
        </div>

        <div style={{ display: window.innerWidth < 640 ? 'block' : 'flex', gap: '12px', marginBottom: '20px' }}>
          <div style={{ flex: 1, marginBottom: window.innerWidth < 640 ? '20px' : '0' }}>
            <label style={labelStyle}>Postal Code</label>
            <input
              type="text"
              value={formData.postalCode}
              onChange={(e) => handleInputChange('postalCode', e.target.value)}
              style={inputStyle}
              placeholder="12345"
            />
          </div>
          <div style={{ flex: 1 }}>
            <label style={labelStyle}>Country</label>
            <input
              type="text"
              value={formData.country}
              onChange={(e) => handleInputChange('country', e.target.value)}
              style={inputStyle}
              placeholder="Country"
            />
          </div>
        </div>

        {/* Contact Information */}
        <div style={inputGroupStyle}>
          <label style={labelStyle}>
            Phone Number <span style={{ color: '#ef4444' }}>*</span>
          </label>
          <input
            type="tel"
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            style={inputStyle}
            placeholder="+****************"
          />
          {errors.phone && <div style={errorStyle}>{errors.phone}</div>}
        </div>

        <div style={inputGroupStyle}>
          <label style={labelStyle}>
            Email Address <span style={{ color: '#ef4444' }}>*</span>
          </label>
          <input
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            style={inputStyle}
            placeholder="<EMAIL>"
          />
          {errors.email && <div style={errorStyle}>{errors.email}</div>}
        </div>

        <div style={inputGroupStyle}>
          <label style={labelStyle}>Tax ID / Business Registration Number</label>
          <input
            type="text"
            value={formData.taxId}
            onChange={(e) => handleInputChange('taxId', e.target.value)}
            style={inputStyle}
            placeholder="123-45-6789"
          />
        </div>

        <div style={inputGroupStyle}>
          <label style={labelStyle}>Website URL</label>
          <input
            type="url"
            value={formData.website}
            onChange={(e) => handleInputChange('website', e.target.value)}
            style={inputStyle}
            placeholder="https://www.yourcompany.com"
          />
        </div>

        {/* Action Buttons */}
        <div style={buttonGroupStyle}>
          <button
            type="button"
            onClick={handleClose}
            style={secondaryButtonStyle}
            disabled={isLoading}
          >
            Cancel
          </button>
          <button
            type="button"
            onClick={handleSave}
            style={primaryButtonStyle}
            disabled={isLoading || !formData.name || !formData.email || !formData.phone}
          >
            {isLoading ? 'Saving...' : 'Save'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CompanyInfoModal;
